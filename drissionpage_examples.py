#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DrissionPage 基本功能示例
"""

def example_session_page():
    """SessionPage 示例 - 收发数据包"""
    print("📡 SessionPage 示例 - 收发数据包")
    print("-" * 30)
    
    from DrissionPage import SessionPage
    
    # 创建SessionPage对象
    page = SessionPage()
    
    # 访问网页
    response = page.get('https://httpbin.org/get')
    print(f"✅ 访问成功")
    print(f"📄 页面标题: {page.title}")
    
    # 获取页面内容
    content = page.html
    print(f"📝 页面内容长度: {len(content)} 字符")
    
    print()

def example_chromium_page():
    """ChromiumPage 示例 - 控制浏览器"""
    print("🌐 ChromiumPage 示例 - 控制浏览器")
    print("-" * 30)
    
    from DrissionPage import ChromiumPage
    
    # 创建ChromiumPage对象
    page = ChromiumPage()
    
    # 访问DrissionPage官网
    print("🔗 正在访问 DrissionPage 官网...")
    page.get('http://DrissionPage.cn')
    
    # 获取页面信息
    print(f"📄 页面标题: {page.title}")
    print(f"🔗 当前URL: {page.url}")
    
    # 查找元素
    try:
        # 查找页面中的链接
        links = page.eles('tag:a')
        print(f"🔗 找到 {len(links)} 个链接")
        
        # 显示前5个链接的文本
        for i, link in enumerate(links[:5], 1):
            text = link.text.strip()
            if text:
                print(f"   {i}. {text}")
    except Exception as e:
        print(f"⚠️ 查找元素时出错: {e}")
    
    # 等待3秒让用户看到页面
    print("⏳ 等待3秒...")
    page.wait(3)
    
    # 关闭浏览器
    print("🔚 关闭浏览器")
    page.quit()
    
    print()

def example_web_page():
    """WebPage 示例 - 模式切换"""
    print("🚀 WebPage 示例 - 模式切换")
    print("-" * 30)
    
    from DrissionPage import WebPage
    
    # 创建WebPage对象（默认s模式）
    page = WebPage()
    
    print("📡 当前模式: s模式 (SessionPage)")
    
    # 在s模式下访问网页
    page.get('https://httpbin.org/get')
    print(f"📄 页面标题: {page.title}")
    
    # 切换到d模式
    print("🔄 切换到 d模式 (ChromiumPage)")
    page.change_mode('d')
    
    # 在d模式下访问网页
    page.get('http://DrissionPage.cn')
    print(f"📄 页面标题: {page.title}")
    
    # 等待2秒
    page.wait(2)
    
    # 关闭浏览器
    page.quit()
    
    print()

def example_element_operations():
    """元素操作示例"""
    print("🎯 元素操作示例")
    print("-" * 30)
    
    from DrissionPage import ChromiumPage
    
    page = ChromiumPage()
    
    # 访问一个有表单的测试页面
    print("🔗 访问测试页面...")
    page.get('https://httpbin.org/forms/post')
    
    try:
        # 查找输入框并输入文本
        input_field = page.ele('tag:input@@name=custname')
        if input_field:
            input_field.input('DrissionPage测试')
            print("✅ 输入文本成功")
        
        # 查找文本域并输入内容
        textarea = page.ele('tag:textarea')
        if textarea:
            textarea.input('这是一个DrissionPage自动化测试')
            print("✅ 文本域输入成功")
        
        # 等待2秒让用户看到输入结果
        print("⏳ 等待2秒查看输入结果...")
        page.wait(2)
        
    except Exception as e:
        print(f"⚠️ 元素操作时出错: {e}")
    
    # 关闭浏览器
    page.quit()
    
    print()

def main():
    """主函数"""
    print("🎉 DrissionPage 功能示例演示")
    print("=" * 50)
    
    try:
        # SessionPage示例
        example_session_page()
        
        # ChromiumPage示例
        example_chromium_page()
        
        # WebPage示例
        example_web_page()
        
        # 元素操作示例
        example_element_operations()
        
        print("🎊 所有示例演示完成！")
        print("📚 更多功能请参考文档: http://DrissionPage.cn")
        
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断了演示")
    except Exception as e:
        print(f"❌ 演示过程中出错: {e}")

if __name__ == "__main__":
    main()
