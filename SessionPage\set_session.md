---
id: page_settings
title: '🚄 页面设置'
---

<div class="wwads-cn wwads-horizontal" data-id="317"></div><br/>

本节介绍`SessionPage`运行参数设置。

这些设置是全局参数，设置后每次请求都会使用它们。

**示例：**

```python
from DrissionPage import SessionPage

page = SessionPage()
page.set.cookies([{'name': 'a', 'value': '1'}, {'name': 'b', 'value': '2'}])
```

## ✅️️ `set.retry_times()`

此方法用于设置连接失败时重连次数。

| 参数名称    | 类型    | 默认值 | 说明 |
| ------- | ----- | --- |----|
| `times` | `int` | 必填  | 次数 |

**返回：**`None`

## ✅️️ `set.retry_interval()`

此方法用于设置连接失败时重连间隔。

| 参数名称       | 类型      | 默认值 | 说明  |
| ---------- | ------- | --- | --- |
| `interval` | `float` | 必填  | 秒数  |

**返回：**`None`

## ✅️️ `set.timeout()`

此方法用于设置连接超时时间。

| 参数名称     | 类型      | 默认值 | 说明  |
|:--------:|:-------:|:---:| --- |
| `second` | `float` | 必填  | 秒数  |

**返回：**`None`

**示例：**

```python
page.set.timeout(20)
```

---

## ✅️️ `set.encoding()`

此方法用于设置网页编码。

默认情况下，程序会自动从 headers、页面上获取编码，但总有些奇葩网页的编码不准确。这时候可以主动设置编码。

可以针对已获取的`Rsponse`对象设置，或作为整体设置对之后的连接都有效。

|    参数名称    |   类型   |  默认值  | 说明                                  |
|:----------:|:------:|:-----:|-------------------------------------|
| `encoding` | `str`  |  必填   | 编码名称，如果要取消之前的设置，传入`None`            |
| `set_all`  | `bool` | `True` | 是否设置对象参数，为`False`则只设置当前`Response`对象 |

**返回：**`None`

---

## ✅️️ `set.cookies()`

此方法用于设置一个或多个 cookie。

设置一个 cookie 支持的格式：

- `Cookie`：单个`Cookie`对象
- `str`：`'name=value; domain=****; ...'`或`'name=****; value=****; domain=****; ...'`格式，只支持用`';'`分隔
- `dict`：`{'name': '****', 'value': '****', 'domain': '****', ...}`或`{name: value, 'domain': '****', ...}`格式

设置多个 cookie 支持的格式：

- `list`或`tuple`：上面几种形式的单个 cookie 放到列表中传入即可
- `dict`：`{name1: value1, name2: value2, ..., 'domain': '****', ...}`格式
- `str`：`'name1=value1; name2=value2; ... domain=****; ...'`格式，多个 cookie 之间只能用`';'`分隔
- `CookieJar`：单个`CookieJar`对象

| 参数名称      | 类型                                                          | 默认值 | 说明         |
|:---------:|:-----------------------------------------------------------:|:---:|------------|
| `cookies` | `Cookie`<br/>`CookieJar`<br/>`list`<br/>`tuple`<br/>`str`<br/>`dict` | 必填  | cookies 信息 |

**返回：**`None`

---

## ✅️️ `set.cookies.clear()`

此方法用于清除所有 cookie。

**参数：** 无

**返回：**`None`

---

## ✅️️ `set.cookies.remove()`

此方法用于删除一个 cookie。

|  参数名称  |  类型   | 默认值 | 说明               |
|:------:|:-----:|:---:|------------------|
| `name` | `str` | 必填  | cookie 的 name 字段 |

**返回：**`None`

---

## ✅️️ `set.headers()`

此方法用于设置 headers，会取代已有 headers。

headers 可以是`dict`格式的，也可以是文本格式。

文本格式不同字段用`\n`分隔，字段 key 和 value 用`': '`分隔，即从浏览器直接复制的格式。

| 参数名称      | 类型     | 默认值 | 说明         |
|:---------:|:------:|:---:| ---------- |
| `headers` | `dict`<br/>`str` | 必填  | headers 信息 |

**返回：**`None`

---

## ✅️️ `set.header()`

此方法用于设置 headers 中一个项。

|  参数名称   | 类型    | 默认值 | 说明   |
|:-------:|:-----:|:---:| ---- |
| `name`  | `str` | 必填  | 设置名称 |
| `value` | `str` | 必填  | 设置值  |

**返回：**`None`

---

## ✅️️ `set.user_agent()`

此方法用于设置 user_agent。

| 参数名称 | 类型    | 默认值 | 说明            |
|:----:|:-----:|:---:| ------------- |
| `ua` | `str` | 必填  | user_agent 信息 |

**返回：**`None`

---

## ✅️️ `set.proxies()`

此方法用于设置代理 ip。

| 参数名称    | 类型    | 默认值    | 说明                           |
|:-------:|:-----:|:------:| ---------------------------- |
| `http`  | `str` | 必填     | http代理地址                     |
| `https` | `str` | `None` | https代理地址，为`None`时使用`http`的值 |

**返回：**`None`

---

## ✅️️ `set.auth()`

此方法用于设置认证元组或对象。

| 参数名称   | 类型                                   | 默认值 | 说明      |
|:------:|:------------------------------------:|:---:| ------- |
| `auth` | `Tuple[str, str]`<br/>`HTTPBasicAuth` | 必填  | 认证元组或对象 |

**返回：**`None`

---

## ✅️️ `set.hooks()`

此方法用于设置回调方法。

| 参数名称    | 类型     | 默认值 | 说明   |
|:-------:|:------:|:---:| ---- |
| `hooks` | `dict` | 必填  | 回调方法 |

**返回：**`None`

---

## ✅️️ `set.params()`

此方法用于设置查询参数字典。

| 参数名称     | 类型     | 默认值 | 说明     |
|:--------:|:------:|:---:| ------ |
| `params` | `dict` | 必填  | 查询参数字典 |

**返回：**`None`

---

## ✅️️ `set.verify()`

此方法用于设置是否验证SSL证书。

| 参数名称     | 类型     | 默认值 | 说明          |
|:--------:|:------:|:---:| ----------- |
| `on_off` | `bool` | 必填  | `bool`表示开或关 |

**返回：**`None`

---

## ✅️️ `set.cert()`

此方法用于设置SSL客户端证书。

| 参数名称   | 类型                         | 默认值 | 说明                                       |
|:------:|:--------------------------:|:---:| ---------------------------------------- |
| `cert` | `str`<br/>`Tuple[str, str]` | 必填  | SSL客户端证书文件的路径(.pem格式)，或(‘cert’, ‘key’)元组 |

**返回：**`None`

---

## ✅️️ `set.stream()`

此方法用于设置是否使用流式响应内容。

| 参数名称     | 类型     | 默认值 | 说明          |
|:--------:|:------:|:---:| ----------- |
| `on_off` | `bool` | 必填  | `bool`表示开或关 |

**返回：**`None`

---

## ✅️️ `set.trust_env()`

此方法用于设置是否信任环境。

| 参数名称     | 类型     | 默认值 | 说明          |
|:--------:|:------:|:---:| ----------- |
| `on_off` | `bool` | 必填  | `bool`表示开或关 |

**返回：**`None`

---

## ✅️️ `set.max_redirects()`

此方法用于设置连接超时时间。

| 参数名称    | 类型    | 默认值 | 说明      |
|:-------:|:-----:|:---:| ------- |
| ``times | `int` | 必填  | 最大重定向次数 |

**返回：**`None`

---

## ✅️️ `set.add_adapter()`

此方法用于添加适配器。

| 参数名称      | 类型            | 默认值 | 说明       |
|:---------:|:-------------:|:---:| -------- |
| `url`     | `str`         | 必填  | 适配器对应url |
| `adapter` | `HTTPAdapter` | 必填  | 适配器对象    |

**返回：**`None`

---

## ✅️️ `close()`

此方法用于关闭连接。

**参数：** 无

**返回：**`None`

---