#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DrissionPage 环境测试脚本
"""

def test_import():
    """测试导入功能"""
    try:
        from DrissionPage import ChromiumPage, SessionPage, WebPage
        print("✅ DrissionPage 导入成功！")
        return True
    except ImportError as e:
        print(f"❌ DrissionPage 导入失败: {e}")
        return False

def test_session_page():
    """测试SessionPage功能（收发数据包）"""
    try:
        from DrissionPage import SessionPage
        page = SessionPage()
        # 测试访问一个简单的网页
        response = page.get('https://httpbin.org/get')
        if response and hasattr(response, 'status_code') and response.status_code == 200:
            print("✅ SessionPage 功能正常！")
            return True
        elif response:
            print("✅ SessionPage 功能正常！（响应格式不同）")
            return True
        else:
            print("❌ SessionPage 测试失败，无响应")
            return False
    except Exception as e:
        print(f"❌ SessionPage 测试失败: {e}")
        return False

def test_browser_detection():
    """测试浏览器检测"""
    try:
        from DrissionPage import ChromiumPage
        print("🔍 正在检测浏览器...")
        
        # 尝试创建ChromiumPage对象
        page = ChromiumPage()
        print("✅ 浏览器检测成功！")
        
        # 尝试访问测试页面
        page.get('http://DrissionPage.cn')
        print("✅ 浏览器控制功能正常！")
        
        # 关闭浏览器
        page.quit()
        return True
        
    except Exception as e:
        print(f"❌ 浏览器测试失败: {e}")
        print("💡 可能需要设置浏览器路径，请参考文档中的准备工作部分")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试 DrissionPage 环境...")
    print("=" * 50)
    
    # 测试导入
    if not test_import():
        return
    
    print()
    
    # 测试SessionPage
    print("📡 测试 SessionPage 功能...")
    test_session_page()
    
    print()
    
    # 测试浏览器功能
    print("🌐 测试浏览器控制功能...")
    test_browser_detection()
    
    print()
    print("=" * 50)
    print("🎉 环境测试完成！")

if __name__ == "__main__":
    main()
