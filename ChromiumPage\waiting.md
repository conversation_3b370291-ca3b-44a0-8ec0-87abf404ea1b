---
id: waiting
title: '🚤 等待'
---

<div class="wwads-cn wwads-horizontal" data-id="317"></div><br/>

网络环境不稳定，页面 js 运行时间也难以确定，自动化过程中经常遇到需要等待的情况。

如果总是用`sleep()`，显得不太优雅，等待多了浪费时间，等待不够会导致报错。

因此，程序能够智能等待是非常重要的，DrissionPage 内置了一些等待方法，可以提高程序稳定性和效率。

它们藏在页面对象和元素对象的`wait`属性里。

等待方法均有`timeout`参数，可自行设得超时时间，也可以设置超时后返回`False`还是抛出异常。

## ✅️️ 页面对象的等待方法

**示例：**

```python
from DrissionPage import ChromiumPage

page = ChromiumPage()
page.get('http://DrissionPage.cn')
page.wait.ele_displayed('tag:div')
```

### 📌 `wait.load_start()`

此方法用于等待页面进入加载状态。  
我们经常会通过点击元素进入下一个网页，并立刻获取新页面的元素。  
但若跳转前的页面拥有和跳转后页面相同定位符的元素，会导致过早获取元素，跳转后失效的问题。  
使用此方法，会阻塞程序，等待页面开始加载后再继续，从而避免上述问题。  
我们通常只需等待页面加载开始，程序会自动等待加载结束。

:::info 注意
    `get()`已内置等待加载开始，后无须跟`wait.load_start()`。
:::

| 参数名称      | 类型                          | 默认值    | 说明                                                |
|:---------:|:---------------------------:|:------:| ------------------------------------------------- |
| `timeout` | `float`<br/>`None`<br/>`True` | `None` | 超时时间，为`None`或`Ture`时使用页面`timeout`设置<br/>为数字时等待相应时间 |
| `raise_err` | `bool`  | `None` | 等待失败时是否报错，为`None`时根据`Settings`设置 |

| 返回类型   | 说明            |
|:------:| ------------- |
| `bool` | 等待结束时是否进入加载状态 |

**示例：**

```python
ele.click()  # 点击某个元素
page.wait.load_start()  # 等待页面进入加载状态
# 执行在新页面的操作
print(page.title)
```

---

### 📌 `wait.doc_loaded()`

此方法用于等待页面文档加载完成。  
一般来说都无需开发者使用，程序大部分动作都会自动等待加载完成再执行。

:::info 注意
    - 此功能仅用于等待页面主 document 加载，不能用于等待 js 加载的变化。
    - 除非`load_mode`为`None`，`get()`方法已内置等待加载完成，后面无须添加等待。
:::

| 参数名称      | 类型                          | 默认值    | 说明                                                |
|:---------:|:---------------------------:|:------:| ------------------------------------------------- |
| `timeout` | `float`<br/>`None`<br/>`True` | `None` | 超时时间，为`None`或`Ture`时使用页面`timeout`设置<br/>为数字时等待相应时间 |
| `raise_err` | `bool`  | `None` | 等待失败时是否报错，为`None`时根据`Settings`设置 |

| 返回类型   | 说明            |
|:------:| ------------- |
| `bool` | 等待结束时是否完成加载完成 |

---

### 📌 `wait.eles_loaded()`

此方法用于等待元素被加载到 DOM，可等待全部或任意一个加载。  
有时一个元素的正常出现是下一步操作的前提，用此方法可以防止一些元素加载速度慢于程序动作速度导致的误操作。

|    参数名称     |                  类型                   |   默认值   | 说明                           |
|:-----------:|:-------------------------------------:|:-------:| ---------------------------- |
|  `locator`  | `str`<br/>`Tuple[str, str]`<br/>`list` |   必填    | 要等待的元素，定位符                   |
|  `timeout`  |                `float`                | `None`  | 超时时间，为`None`时使用页面`timeout`设置 |
|  `any_one`  |                `bool`                 | `False` | 是否等待到一个就返回 |
| `raise_err` |                `bool`                 | `None`  | 等待失败时是否报错，为`None`时根据`Settings`设置 |

|  返回类型  | 说明     |
|:------:|--------|
| `bool` | 是否等待成功 |

**示例：**

```python
ele1.click()  # 点击某个元素
page.wait.eles_loaded('#div1')  # 等待 id 为 div1 的元素加载
ele2.click()  # div1 加载完成后再执行下一步操作
```

---

### 📌 `wait.ele_displayed()`

此方法用于等待一个元素变成显示状态。  
如果当前 DOM 中查找不到指定元素，则会自动等待元素加载，再等待它显示。  
元素隐藏是指元素在 DOM 内，但处于隐藏状态（即使在视口内且不被遮挡）。  
父元素隐藏时子元素也是隐藏的。

| 参数名称         | 类型                                              | 默认值    | 说明                           |
|:------------:|:-----------------------------------------------:|:------:| ---------------------------- |
| `loc_or_ele` | `str`<br/>`Tuple[str, str]`<br/>`ChromiumElement` | 必填     | 要等待的元素，可以是元素或定位符             |
| `timeout`    | `float`                                         | `None` | 超时时间，为`None`时使用页面`timeout`设置 |
| `raise_err` | `bool`  | `None` | 等待失败时是否报错，为`None`时根据`Settings`设置 |

| 返回类型   | 说明     |
|:------:| ------ |
| `bool` | 是否等待成功 |

**示例：**

```python
# 等待 id 为 div1 的元素显示，超时使用页面设置
page.wait.ele_displayed('#div1')

# 等待 id 为 div1 的元素显示，设置超时3秒
page.wait.ele_displayed('#div1', timeout=3)

# 等待已获取到的元素被显示
ele = page.ele('#div1')
page.wait.ele_displayed(ele)
```

---

### 📌 `wait.ele_hidden()`

此方法用于等待一个元素变成隐藏状态。  
如果当前 DOM 中查找不到指定元素，则会自动等待元素加载，再等待它隐藏。  
元素隐藏是指元素在 DOM 内，但处于隐藏状态（即使在视口内且不被遮挡）。  
父元素隐藏时子元素也是隐藏的。

| 参数名称         | 类型                                              | 默认值    | 说明                           |
|:------------:|:-----------------------------------------------:|:------:| ---------------------------- |
| `loc_or_ele` | `str`<br/>`Tuple[str, str]`<br/>`ChromiumElement` | 必填     | 要等待的元素，可以是元素或定位符             |
| `timeout`    | `float`                                         | `None` | 超时时间，为`None`时使用页面`timeout`设置 |
| `raise_err` | `bool`  | `None` | 等待失败时是否报错，为`None`时根据`Settings`设置 |

| 返回类型   | 说明     |
|:------:| ------ |
| `bool` | 是否等待成功 |

---

### 📌 `wait.ele_deleted()`

此方法用于等待一个元素被从 DOM 中删除。

| 参数名称         | 类型                                              | 默认值    | 说明                           |
|:------------:|:-----------------------------------------------:|:------:| ---------------------------- |
| `loc_or_ele` | `str`<br/>`Tuple[str, str]`<br/>`ChromiumElement` | 必填     | 要等待的元素，可以是元素或定位符             |
| `timeout`    | `float`                                         | `None` | 超时时间，为`None`时使用页面`timeout`设置 |
| `raise_err` | `bool`  | `None` | 等待失败时是否报错，为`None`时根据`Settings`设置 |

| 返回类型   | 说明     |
|:------:| ------ |
| `bool` | 是否等待成功 |

---

### 📌 `wait.download_begin()`

此方法用于等待下载开始，详见下载功能章节。

| 参数名称      | 类型      | 默认值    | 说明                           |
|:---------:|:-------:|:------:| ---------------------------- |
| `timeout` | `float` | `None` | 超时时间，为`None`时使用页面`timeout`设置 |
| `raise_err` | `bool`  | `None` | 等待失败时是否报错，为`None`时根据`Settings`设置 |

| 返回类型   | 说明     |
|:------:| ------ |
| `bool` | 是否等待成功 |

**示例：**

```python
page('#download_btn').click()  # 点击按钮触发下载
page.wait.download_begin()  # 等待下载开始
```

---

### 📌 `wait.upload_paths_inputted()`

此方法用于等待自动填写上传文件路径。详见文件上传章节。

**参数：** 无

**返回：**`None`

**示例：**

```python
# 设置要上传的文件路径
page.set.upload_files('demo.txt')
# 点击触发文件选择框按钮
btn_ele.click()
# 等待路径填入
page.wait.upload_paths_inputted()
```

---

### 📌 `wait.new_tab()`

此方法用于等待新标签页出现。

| 参数名称      |   类型    | 默认值    | 说明                            |
|:---------:|:-------:|:------:|-------------------------------|
| `timeout` | `float` | `None` | 超时时间，为`None`时使用页面`timeout`设置  |
| `raise_err` | `bool`  | `None` | 等待失败时是否报错，为`None`时根据`Settings`设置 |

|  返回类型   | 说明           |
|:-------:|--------------|
|  `str`  | 等待成返回新标签页 id |
| `False` | 等待失败返回`False` |

---

### 📌 `wait.title_change()`

此方法用于等待 title 变成包含或不包含指定文本。

| 参数名称      |   类型   |   默认值   | 说明                                          |
|:---------:|:------:|:-------:|---------------------------------------------|
| `text` | `str`  |   必填    | 用于识别的文本                                     |
| `exclude` | `bool` | `False` | 是否排除，为`True`时当 title 不包含`text`指定文本时返回`True` |
| `timeout` | `bool` | `float` | 超时时间                                        |
| `raise_err` | `bool` | `None`  | 等待失败时是否报错，为`None`时根据`Settings`设置            |

| 返回类型   | 说明     |
|:------:| ------ |
| `bool` | 是否等待成功 |

---

### 📌 `wait.url_change()`

此方法用于等待 url 变成包含或不包含指定文本。  
比如有些网站登录时会进行多重跳转，url 发生多次变化，可用此功能等待到达最终需要的页面。

| 参数名称      |   类型   |   默认值   | 说明                                        |
|:---------:|:------:|:-------:|-------------------------------------------|
| `text` | `str`  |   必填    | 用于识别的文本                                   |
| `exclude` | `bool` | `False` | 是否排除，为`True`时当 url 不包含`text`指定文本时返回`True` |
| `timeout` | `bool` | `float` | 超时时间                                      |
| `raise_err` | `bool` | `None`  | 等待失败时是否报错，为`None`时根据`Settings`设置          |

| 返回类型   | 说明     |
|:------:| ------ |
| `bool` | 是否等待成功 |

**示例：**

```python
# 访问网站
page.get('https://www.*****.cn/login/')  # 访问登录页面
page.ele('#username').input('***')  # 执行登录逻辑
page.ele('#password').input('***\n')

page.wait.url_change('https://www.*****.cn/center/')  # 等待url变成后台url
```

---

### 📌 `wait.alert_closed()`

此方法用于等待弹出框被关闭。

**参数：** 无

**返回：**`None`

--- 

### 📌 `wait()`

此方法用于等待若干秒。  
`scope`为`None`时，效果与`time.sleep()`没有区别，等待指定秒数。  
`scope`不为`None`时，获取两个参数之间的一个随机值，等待这个数值的秒数。

|   参数名称   | 类型      |  默认值  | 说明                                |
|:--------:|:-------:|:-----:|-----------------------------------|
| `second` | `float` |  必填   | 要等待的秒数，`scope`不为`None`时表示随机数范围起始值 |
| `scope` | `float` | `None` | 随机数范围结束值                          |

**返回：**`None`

**示例：**

```python
page.wait(1)  # 强制等待1秒

page.wait(3.5, 8.5)  # 获取3.5至8.5之间的一个随机数，等待这个数值的秒数
```

--- 

## ✅️️ 元素对象的等待方法

```python
from DrissionPage import ChromiumPage

page = ChromiumPage()
page.get('http://DrissionPage.cn')
ele = page('tag:div')
ele.wait.covered()
```

### 📌 `wait.displayed()`

此方法用于等待元素从隐藏状态变成显示状态。  
元素隐藏是指元素在 DOM 内，但处于隐藏状态（即使在视口内且不被遮挡）。父元素隐藏时子元素也是隐藏的。

| 参数名称      | 类型      | 默认值    | 说明                          |
|:---------:|:-------:|:------:| --------------------------- |
| `timeout` | `float` | `None` | 等待超时时间，为`None`则使用元素所在页面超时时间 |
| `raise_err` | `bool`  | `None` | 等待失败时是否报错，为`None`时根据`Settings`设置 |

| 返回类型   | 说明     |
|:------:|:------:|
| `bool` | 是否等待成功 |

**示例：**

```python
# 等待元素显示，超时使用ele所在页面设置
ele.wait.displayed()
```

--- 

### 📌 `wait.hidden()`

此方法用于等待元素从显示状态变成隐藏状态。  
元素隐藏是指元素在 DOM 内，但处于隐藏状态（即使在视口内且不被遮挡）。父元素隐藏时子元素也是隐藏的。

| 参数名称      | 类型      | 默认值    | 说明                          |
|:---------:|:-------:|:------:| --------------------------- |
| `timeout` | `float` | `None` | 等待超时时间，为`None`则使用元素所在页面超时时间 |
| `raise_err` | `bool`  | `None` | 等待失败时是否报错，为`None`时根据`Settings`设置 |

| 返回类型   | 说明     |
|:------:|:------:|
| `bool` | 是否等待成功 |

**示例：**

```python
# 等待元素不显示，超时为3秒
ele.wait.hidden(timeout=3)
```

---

### 📌 `wait.deleted()`

此方法用于等待元素被从 DOM 删除。

| 参数名称      | 类型      | 默认值    | 说明                          |
|:---------:|:-------:|:------:| --------------------------- |
| `timeout` | `float` | `None` | 等待超时时间，为`None`则使用元素所在页面超时时间 |
| `raise_err` | `bool`  | `None` | 等待失败时是否报错，为`None`时根据`Settings`设置 |

| 返回类型   | 说明     |
|:------:|:------:|
| `bool` | 是否等待成功 |

**示例：**

```python
# 等待元素显示，超时使用ele所在页面设置
ele.wait.deleted()
```

---

### 📌 `wait.covered()`

此方法用于等待元素被其它元素覆盖。

| 参数名称      | 类型      | 默认值    | 说明                          |
|:---------:|:-------:|:------:| --------------------------- |
| `timeout` | `float` | `None` | 等待超时时间，为`None`则使用元素所在页面超时时间 |
| `raise_err` | `bool`  | `None` | 等待失败时是否报错，为`None`时根据`Settings`设置 |

| 返回类型   | 说明     |
|:------:|:------:|
| `bool` | 是否等待成功 |

---

### 📌 `wait.not_covered()`

此方法用于等待元素不被其它元素覆盖。  
可用于等待遮挡被操作元素的“加载中”遮罩消失。

| 参数名称      | 类型      | 默认值    | 说明                          |
|:---------:|:-------:|:------:| --------------------------- |
| `timeout` | `float` | `None` | 等待超时时间，为`None`则使用元素所在页面超时时间 |
| `raise_err` | `bool`  | `None` | 等待失败时是否报错，为`None`时根据`Settings`设置 |

| 返回类型   | 说明     |
|:------:|:------:|
| `bool` | 是否等待成功 |

---

### 📌 `wait.enabled()`

此方法用于等待元素变为可用状态。  
不可用状态的元素仍然在 DOM 内，`disabled`属性为`False`。

| 参数名称      | 类型      | 默认值    | 说明                          |
|:---------:|:-------:|:------:| --------------------------- |
| `timeout` | `float` | `None` | 等待超时时间，为`None`则使用元素所在页面超时时间 |
| `raise_err` | `bool`  | `None` | 等待失败时是否报错，为`None`时根据`Settings`设置 |

| 返回类型   | 说明     |
|:------:|:------:|
| `bool` | 是否等待成功 |

---

### 📌 `wait.disabled()`

此方法用于等待元素变为不可用状态。  
不可用状态的元素仍然在 DOM 内，`disabled`属性为`True`。

| 参数名称      | 类型      | 默认值    | 说明                          |
|:---------:|:-------:|:------:| --------------------------- |
| `timeout` | `float` | `None` | 等待超时时间，为`None`则使用元素所在页面超时时间 |
| `raise_err` | `bool`  | `None` | 等待失败时是否报错，为`None`时根据`Settings`设置 |

| 返回类型   | 说明     |
|:------:|:------:|
| `bool` | 是否等待成功 |

---

### 📌 `wait.stop_moving()`

此方法用于等待元素运动结束。如果元素没有大小和位置信息，会在超时时抛出`NoRectError`异常。

| 参数名称      | 类型      |  默认值   | 说明                               |
|:---------:|:-------:|:------:|----------------------------------|
| `timeout` | `float` | `None` | 等待超时时间，为`None`则使用元素所在页面超时时间      |
| `gap` | `float` | `0.1`  | 检测运动的间隔时间                        |
| `raise_err` | `bool`  | `None` | 等待失败时是否报错，为`None`时根据`Settings`设置 |

| 返回类型   | 说明     |
|:------:|:------:|
| `bool` | 是否等待成功 |

```python
# 等待元素稳定
page.ele('#button1').wait.stop_moving()
# 点击元素
page.ele('#button1').click()
```

---

### 📌 `wait.clickable()`

此方法用于等待元素可被点击。

| 参数名称      |   类型    |  默认值   | 说明                               |
|:---------:|:-------:|:------:|----------------------------------|
| `wait_moved` | `bool`  | `True`  | 是否等待元素运动结束                        |
| `timeout` | `float` | `None` | 等待超时时间，为`None`则使用元素所在页面超时时间      |
| `raise_err` | `bool`  | `None` | 等待失败时是否报错，为`None`时根据`Settings`设置 |

| 返回类型   | 说明     |
|:------:|:------:|
| `bool` | 是否等待成功 |

---

### 📌 `wait.disabled_or_deleted()`

此方法用于等待元素变为不可用或被删除。

| 参数名称      | 类型      | 默认值    | 说明                          |
|:---------:|:-------:|:------:| --------------------------- |
| `timeout` | `float` | `None` | 等待超时时间，为`None`则使用元素所在页面超时时间 |
| `raise_err` | `bool`  | `None` | 等待失败时是否报错，为`None`时根据`Settings`设置 |

| 返回类型   | 说明     |
|:------:|:------:|
| `bool` | 是否等待成功 |

---

### 📌 `wait()`

此方法用于等待若干秒。  
`scope`为`None`时，效果与`time.sleep()`没有区别，等待指定秒数。  
`scope`不为`None`时，获取两个参数之间的一个随机值，等待这个数值的秒数。

|   参数名称   | 类型      |  默认值  | 说明                                |
|:--------:|:-------:|:-----:|-----------------------------------|
| `second` | `float` |  必填   | 要等待的秒数，`scope`不为`None`时表示随机数范围起始值 |
| `scope` | `float` | `None` | 随机数范围结束值                          |

**返回：**`None`

**示例：**

```python
ele.wait(1)  # 强制等待1秒

ele.wait(3.5, 8.5)  # 获取3.5至8.5之间的一个随机数，等待这个数值的秒数
```