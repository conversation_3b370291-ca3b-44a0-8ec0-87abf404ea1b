---
id: installation
title: 🌏 安装
---

<div class="wwads-cn wwads-horizontal" data-id="317"></div><br/>

## ✅️️ 运行环境

操作系统：Windows、Linux 或 Mac。

python 版本：3.6 及以上

支持浏览器：Chromium 内核（如 Chrome 和 Edge）

---

## ✅️️ 安装

请使用 pip 安装 DrissionPage：

```shell
pip install DrissionPage
```

---

## ✅️️ 升级

### 📌 升级最新稳定版

```shell
pip install DrissionPage --upgrade
```

---

### 📌 指定版本升级

```shell
pip install DrissionPage==4.0.0b17
```
