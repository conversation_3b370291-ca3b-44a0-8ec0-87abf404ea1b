#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
抖音热榜内容获取工具 - 简单反反爬版本
基于原始成功的采集逻辑，只添加必要的反反爬功能
"""

import time
import re
import random

def setup_simple_anti_detection():
    """设置简单的反反爬浏览器配置"""
    from DrissionPage import ChromiumPage, ChromiumOptions
    
    # 创建浏览器配置
    co = ChromiumOptions()
    
    # 1. 随机用户代理
    user_agents = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    ]
    co.set_user_agent(random.choice(user_agents))
    
    # 2. 禁用自动化检测特征
    co.set_argument('--disable-blink-features=AutomationControlled')
    co.set_argument('--disable-dev-shm-usage')
    co.set_argument('--no-sandbox')
    
    # 3. 设置语言
    co.set_argument('--lang=zh-CN')
    
    # 创建页面对象
    page = ChromiumPage(co)
    
    # 4. 隐藏webdriver特征
    page.run_js('''
        Object.defineProperty(navigator, 'webdriver', {
            get: () => undefined,
        });
    ''')
    
    return page

def get_douyin_hot_list(url):
    """获取抖音热榜内容 - 使用原始成功的逻辑"""
    print(f"🔗 正在获取抖音热榜: {url}")
    
    # 使用简单的反反爬配置
    page = setup_simple_anti_detection()
    
    try:
        print("🌐 正在访问抖音热榜...")
        page.get(url)
        
        print("⏳ 等待页面加载...")
        # 随机延迟1-3秒
        time.sleep(random.uniform(1, 3))
        
        print(f"📄 页面标题: {page.title}")
        print(f"🔗 当前URL: {page.url}")
        
        hot_list = []
        
        # 使用原始脚本中成功的方法：查找li元素
        print("\n🔍 查找热榜列表元素...")
        selectors = [
            '.billboard-item',
            '.hot-item',
            '.rank-item',
            '[class*="item"]',
            '[class*="rank"]',
            '[class*="hot"]',
            'li',  # 这个在原始脚本中成功了
            '.list-item'
        ]
        
        for selector in selectors:
            try:
                items = page.eles(selector)
                if items and len(items) > 5:  # 如果找到多个项目
                    print(f"   通过 {selector} 找到 {len(items)} 个项目")
                    
                    for i, item in enumerate(items[:50]):  # 增加到50个
                        try:
                            text = item.text.strip()
                            if text and len(text) > 5 and len(text) < 200:
                                # 过滤掉一些无用信息
                                if not any(keyword in text.lower() for keyword in ['登录', 'login', '下载', 'download', '分享']):
                                    hot_list.append({
                                        'rank': i + 1,
                                        'title': text,
                                        'source': selector
                                    })
                        except:
                            continue
                    
                    if hot_list:
                        break
            except:
                continue
        
        return hot_list
        
    except Exception as e:
        print(f"❌ 获取热榜时出错: {e}")
        return []
    
    finally:
        # 关闭浏览器
        try:
            page.quit()
        except:
            pass

def main():
    """主函数"""
    print("🔥 抖音热榜内容获取工具 - 简单反反爬版本")
    print("=" * 60)
    
    # 抖音链接
    douyin_url = "https://v.douyin.com/oGXfNDHtZbQ/"
    
    print(f"📱 目标链接: {douyin_url}")
    print("🛡️ 启用基础反反爬机制...")
    print()
    
    # 获取热榜内容
    hot_list = get_douyin_hot_list(douyin_url)
    
    print("\n" + "=" * 60)
    
    if hot_list:
        print(f"🎯 成功获取到 {len(hot_list)} 个热榜项目:")
        print()
        
        # 显示热榜内容
        for item in hot_list:
            print(f"{item['rank']:2d}. {item['title']}")
        
        # 保存到文件
        try:
            filename = f'douyin_hot_list_simple_{time.strftime("%Y%m%d_%H%M%S")}.txt'
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"抖音热榜内容 - 获取时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"来源链接: {douyin_url}\n")
                f.write("=" * 50 + "\n\n")
                
                for item in hot_list:
                    f.write(f"{item['rank']:2d}. {item['title']}\n")
                
                f.write(f"\n总计: {len(hot_list)} 个项目\n")
                f.write(f"获取方式: {hot_list[0]['source'] if hot_list else 'unknown'}\n")
            
            print(f"\n💾 热榜内容已保存到 {filename} 文件")
            
        except Exception as e:
            print(f"⚠️ 保存文件时出错: {e}")
    else:
        print("❌ 未能获取到热榜内容")
        print("💡 建议:")
        print("   - 尝试运行原始版本: python douyin_hot_list.py")
        print("   - 检查网络连接")

if __name__ == "__main__":
    main()
