#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DrissionPage 浏览器路径设置脚本
"""

import os
import platform
from pathlib import Path

def find_chrome_paths():
    """自动查找Chrome浏览器路径"""
    system = platform.system()
    possible_paths = []
    
    if system == "Windows":
        # Windows常见Chrome路径
        possible_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe".format(os.getenv('USERNAME')),
            r"C:\Program Files\Microsoft\Edge\Application\msedge.exe",
            r"C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe",
        ]
    elif system == "Darwin":  # macOS
        possible_paths = [
            "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
            "/Applications/Microsoft Edge.app/Contents/MacOS/Microsoft Edge",
        ]
    elif system == "Linux":
        possible_paths = [
            "/usr/bin/google-chrome",
            "/usr/bin/google-chrome-stable",
            "/usr/bin/chromium-browser",
            "/usr/bin/chromium",
            "/snap/bin/chromium",
            "/usr/bin/microsoft-edge",
        ]
    
    # 检查哪些路径存在
    existing_paths = []
    for path in possible_paths:
        if os.path.exists(path):
            existing_paths.append(path)
    
    return existing_paths

def set_browser_path(browser_path):
    """设置浏览器路径"""
    try:
        from DrissionPage import ChromiumOptions
        
        # 设置浏览器路径并保存
        ChromiumOptions().set_browser_path(browser_path).save()
        print(f"✅ 浏览器路径设置成功: {browser_path}")
        return True
    except Exception as e:
        print(f"❌ 设置浏览器路径失败: {e}")
        return False

def test_browser_path(browser_path):
    """测试浏览器路径是否有效"""
    try:
        from DrissionPage import ChromiumPage, ChromiumOptions
        
        # 临时使用指定路径测试
        co = ChromiumOptions().set_browser_path(browser_path)
        page = ChromiumPage(co)
        
        # 尝试访问一个简单页面
        page.get('data:text/html,<html><body><h1>测试页面</h1></body></html>')
        
        # 关闭浏览器
        page.quit()
        
        print(f"✅ 浏览器路径测试成功: {browser_path}")
        return True
    except Exception as e:
        print(f"❌ 浏览器路径测试失败: {browser_path}")
        print(f"   错误信息: {e}")
        return False

def main():
    """主函数"""
    print("🔍 DrissionPage 浏览器路径设置工具")
    print("=" * 50)
    
    # 自动查找浏览器路径
    print("🔍 正在自动查找浏览器...")
    chrome_paths = find_chrome_paths()
    
    if chrome_paths:
        print(f"✅ 找到 {len(chrome_paths)} 个可能的浏览器路径:")
        for i, path in enumerate(chrome_paths, 1):
            print(f"   {i}. {path}")
        
        print("\n🧪 正在测试浏览器路径...")
        
        # 测试每个路径
        working_paths = []
        for path in chrome_paths:
            if test_browser_path(path):
                working_paths.append(path)
        
        if working_paths:
            print(f"\n✅ 找到 {len(working_paths)} 个可用的浏览器路径:")
            for i, path in enumerate(working_paths, 1):
                print(f"   {i}. {path}")
            
            # 使用第一个可用路径
            selected_path = working_paths[0]
            print(f"\n🎯 选择路径: {selected_path}")
            
            if set_browser_path(selected_path):
                print("\n🎉 浏览器路径设置完成！")
                print("💡 现在可以正常使用 DrissionPage 控制浏览器了")
            else:
                print("\n❌ 浏览器路径设置失败")
        else:
            print("\n❌ 没有找到可用的浏览器路径")
            print("💡 请手动安装 Chrome 或 Edge 浏览器")
    else:
        print("❌ 未找到浏览器路径")
        print("💡 请确保已安装 Chrome 或 Edge 浏览器")
    
    print("\n" + "=" * 50)
    print("📖 如需手动设置，请参考以下方法:")
    print("1. 打开浏览器，在地址栏输入 chrome://version")
    print("2. 复制可执行文件路径")
    print("3. 运行以下代码:")
    print("   from DrissionPage import ChromiumOptions")
    print("   ChromiumOptions().set_browser_path('你的浏览器路径').save()")

if __name__ == "__main__":
    main()
